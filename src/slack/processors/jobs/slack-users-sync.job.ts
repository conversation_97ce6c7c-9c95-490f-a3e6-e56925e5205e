import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Member } from '@slack/web-api/dist/types/response/UsersListResponse';
import { DeepPartial, Repository } from 'typeorm';
import { Bots, Installations, Users } from '../../../database/entities';
import { BotsRepository } from '../../../database/entities/bots/repositories';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils/logger';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';

/**
 * The maximum number of users to sync in a single batch, this is the limit for the Slack's WebAPI
 * @see https://api.slack.com/methods/users.list#arg_limit
 */
const MAX_USERS_TO_SYNC = 200;

@Injectable()
export class SlackUsersSyncJob {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,

    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,
    private readonly botsRepository: BotsRepository,

    // External Providers
    private readonly slackWebAPIService: SlackWebAPIService,
    private readonly thenaPlatformService: ThenaPlatformApiProvider,
  ) {}

  /**
   * @description
   * Executes the Slack users sync job
   * @param installation The installation to sync the users for
   */
  async execute(installation: Installations) {
    try {
      let hasMore = true;
      let cursor: string | undefined = '';
      let usersCount = 0;

      // While there are more channels to sync, we will continue to sync them
      while (hasMore) {
        // Get the channels from the Slack's WebAPI
        const usersResponse = await this.slackWebAPIService.listUsers(
          installation.botToken,
          { limit: MAX_USERS_TO_SYNC, cursor },
        );

        // If the response is not ok, we will break the loop
        if (!usersResponse.ok) {
          this.logger.error(
            `Slack users sync failed, error: ${usersResponse.error}`,
          );

          // TODO: We should retry the request
          continue;
        }

        // Get the users and the response metadata
        const { members, response_metadata } = usersResponse;

        // Bulk write[upsert]/commit the users to the database
        await this.writeInternalUsers(members, installation);

        // Increment the users count
        usersCount += members.length;

        // If there is no next cursor, we will break the loop
        if (!response_metadata.next_cursor) {
          hasMore = false;
        } else {
          // Otherwise, we will set the cursor to the next cursor
          cursor = response_metadata.next_cursor;
          hasMore = true;
        }
      }

      if (!hasMore) {
        this.logger.log(
          `Slack users sync completed for installation ${installation.name}, ${usersCount} users synced`,
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Slack users sync failed, error: ${error.message}`,
          error.stack,
        );
      }
    }
  }

  /**
   * @description
   * Writes the users to the database
   * @param users The users to write
   * @param installation The installation to write the users for
   */
  private async writeInternalUsers(
    members: Member[],
    installation: Installations,
  ) {
    const users = members.filter((u) => !u.is_bot);
    const bots = members.filter((u) => u.is_bot);

    // Construct the users to write, note this should be a deep partial of the Users entity
    const usersToWrite: Array<DeepPartial<Users>> = users.map((u) =>
      this.constructUser(u, installation),
    );

    // Construct the bots to write, note this should be a deep partial of the Bots entity
    const botsToWrite: Array<DeepPartial<Bots>> = bots.map((b) =>
      this.constructBot(b, installation),
    );

    // Upsert humans ➜ upsert bots ➜ platform-link & metadata update
    await this.usersRepository.upsert(usersToWrite, {
      conflictPaths: ['slackId', 'installationId'],
    });
    await this.botsRepository.upsert(botsToWrite, {
      conflictPaths: ['slackId', 'installationId'],
    });
    await this.linkUsersToPlatform(installation, usersToWrite);
  }

  /**
   * @description
   * Links the users to the platform
   * @param installation The installation to link the users to
   * @param users The users to link
   */
  private async linkUsersToPlatform(
    installation: Installations,
    users: Array<DeepPartial<Users>>,
  ) {
    try {
      // Construct user details
      const details = users
        .filter((u) => Boolean(u.slackProfileEmail))
        .map((u) => ({
          email: u.slackProfileEmail as string,
          slackSinkDetails: {
            id: u.slackId,
            teamId: installation.teamId,
          },
        }));

      if (details.length === 0) {
        this.logger.warn(
          'No users with a valid email, skipping platform linking',
        );
        return;
      }

      // Construct the payload for execution
      const payload = { externalType: 'slack' as const, details };

      // Link the users to platform
      const { data: platformUsers } =
        await this.thenaPlatformService.linkUsersToPlatform(
          installation,
          payload,
        );

      if (Array.isArray(platformUsers)) {
        await this.updateUserMetadataWithPlatformIds(
          installation,
          platformUsers,
          users,
        );
        this.logger.log(
          `Successfully linked ${platformUsers.length} users to platform for installation ${installation.name}`,
        );
      } else {
        throw new Error('Unexpected response format from platform API');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Failed to link users to platform for installation ${installation.name}: ${errorMessage}`,
      );

      // Update user metadata with failed status
      await this.updateUserMetadataWithFailure(
        installation,
        users,
        errorMessage,
      );
    }
  }

  /**
   * @description
   * Constructs a bot object to be written to the database
   * @param bot The bot to construct
   * @param installation The installation to construct the bot for
   * @returns The constructed bot
   */
  private constructBot(
    bot: Member,
    installation: Installations,
  ): DeepPartial<Bots> {
    const images = {
      image_original: bot.profile?.image_original,
      image_512: bot.profile?.image_512,
      image_192: bot.profile?.image_192,
      image_72: bot.profile?.image_72,
      image_48: bot.profile?.image_48,
    };

    return {
      botDump: bot as Record<string, any>,
      slackId: bot.id,
      slackDeleted: bot.deleted,
      name: bot.real_name || bot.name,
      realName: bot.real_name || bot.profile?.real_name,
      displayName: bot.profile?.display_name,
      installation: { id: installation.id },
      organization: { id: installation.organization.id },
      tz: bot.tz,
      tzLabel: bot.tz_label,
      isAdmin: bot.is_admin,
      isOwner: bot.is_owner,
      isRestricted: bot.is_restricted,
      isUltraRestricted: bot.is_ultra_restricted,
      isBot: bot.is_bot,
      botTitle: bot.profile?.title,
      botRealName: bot.profile?.real_name,
      botDisplayName: bot.profile?.display_name,
      images,
      botStatusText: bot.profile?.status_text,
      botStatusEmoji: bot.profile?.status_emoji,
    };
  }

  /**
   * @description
   * Constructs a user object to be written to the database
   * @param user The user to construct
   * @param installation The installation to construct the user for
   * @returns The constructed user
   */
  private constructUser(
    user: Member,
    installation: Installations,
  ): DeepPartial<Users> {
    const images = {
      image_original: user.profile?.image_original,
      image_512: user.profile?.image_512,
      image_192: user.profile?.image_192,
      image_72: user.profile?.image_72,
      image_48: user.profile?.image_48,
      image_32: user.profile?.image_32,
      image_24: user.profile?.image_24,
    };

    return {
      userDump: user as Record<string, any>,
      slackId: user.id,
      slackDeleted: user.deleted,
      name: user.real_name || user.name,
      realName: user.real_name || user.profile?.real_name,
      displayName: user.profile?.display_name,
      installation: { id: installation.id },
      organization: { id: installation.organization.id },
      tz: user.tz,
      tzLabel: user.tz_label,
      isAdmin: user.is_admin,
      isOwner: user.is_owner,
      isRestricted: user.is_restricted,
      isUltraRestricted: user.is_ultra_restricted,
      isBot: user.is_bot,
      userTitle: user.profile?.title,
      slackProfileRealName: user.profile?.real_name,
      slackProfileDisplayName: user.profile?.display_name,
      slackProfilePhone: user.profile?.phone,
      slackStatusText: user.profile?.status_text,
      slackStatusEmoji: user.profile?.status_emoji,
      slackProfileEmail: user.profile?.email,
      images,
    };
  }

  /**
   * @description
   * Updates user metadata with platform user IDs from successful linking
   * @param installation The installation
   * @param platformUsers Array of platform users returned from API
   * @param slackUsers Array of Slack users that were linked
   */
  private async updateUserMetadataWithPlatformIds(
    installation: Installations,
    platformUsers: any[],
    slackUsers: Array<DeepPartial<Users>>,
  ) {
    try {
      // Create a map of email to platform user for quick lookup
      const emailToPlatformUser = new Map();
      for (const platformUser of platformUsers) {
        if (platformUser.email && (platformUser.id || platformUser.uid)) {
          emailToPlatformUser.set(platformUser.email, platformUser);
        }
      }

      // Build array of users to update with new metadata
      const usersToUpdate: Array<DeepPartial<Users>> = [];
      for (const slackUser of slackUsers) {
        if (slackUser.slackProfileEmail && slackUser.slackId) {
          const platformUser = emailToPlatformUser.get(
            slackUser.slackProfileEmail,
          );

          if (platformUser) {
            const metadata = {
              platformUserId: platformUser.id || platformUser.uid,
              lastLinkedAt: new Date().toISOString(),
              linkingStatus: 'success' as const,
            };

            usersToUpdate.push({
              slackId: slackUser.slackId,
              installation: { id: installation.id },
              metadata,
            });

            this.logger.log(
              `Prepared platform user ID update for Slack user ${slackUser.slackId}: ${platformUser.id}`,
            );
          }
        }
      }

      // Bulk update all users with new metadata
      if (usersToUpdate.length > 0) {
        await this.usersRepository.upsert(usersToUpdate, {
          conflictPaths: ['slackId', 'installationId'],
        });
        this.logger.log(
          `Bulk updated ${usersToUpdate.length} users with platform user IDs`,
        );
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Failed to update user metadata with platform IDs: ${errorMessage}`,
      );
    }
  }

  /**
   * @description
   * Updates user metadata with failed linking status
   * @param installation The installation
   * @param slackUsers Array of Slack users that failed to link
   * @param errorMessage The error message
   */
  private async updateUserMetadataWithFailure(
    installation: Installations,
    slackUsers: Array<DeepPartial<Users>>,
    errorMessage: string,
  ) {
    try {
      const slackIds = slackUsers
        .filter((user) => user.slackId)
        .map((user) => user.slackId as string);

      if (slackIds.length > 0) {
        const metadata = {
          lastLinkedAt: new Date().toISOString(),
          linkingStatus: 'failed' as const,
          linkingError: errorMessage,
        };

        await this.usersRepository
          .createQueryBuilder()
          .update(Users)
          .set({ metadata })
          .where('slackId IN (:...slackIds)', { slackIds })
          .andWhere('installationId = :installationId', {
            installationId: installation.id,
          })
          .execute();
      }
    } catch (error) {
      const updateErrorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Failed to update user metadata with failure status: ${updateErrorMessage}`,
      );
    }
  }
}
